import { useState } from 'react'
import { useSelector } from 'react-redux'

import UniversalPopup from '@/components/Popups/UniversalPopup/UniversalPopup'

import { useDeleteTeam } from '@/features/teams/api/deleteTeam'
import { useSwapTeamNumbers } from '@/features/teams/api/swapTeamNumbers'
import { useUpdateTeamNumber } from '@/features/teams/api/updateTeamNumber'
import { useToastContext } from '@/hooks/useToastContext'
import { getThemeMemReg } from '@/reducer/theme/selectors'

import styles from './TeamManagementModal.module.scss'

const TeamManagementModal = ({ team, onClose, allTeams = [] }) => {
  const theme = useSelector((state) => getThemeMemReg(state))
  const toast = useToastContext()
  const [newNumber, setNewNumber] = useState('')
  const [swapTeamId, setSwapTeamId] = useState('')
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  const { mutate: updateTeamNumber, isLoading: isUpdating } = useUpdateTeamNumber()
  const { mutate: swapTeamNumbers, isLoading: isSwapping } = useSwapTeamNumbers()
  const { mutate: deleteTeam, isLoading: isDeleting } = useDeleteTeam()

  const handleChangeNumber = () => {
    if (!newNumber.trim()) return

    updateTeamNumber(
      { publicId: team.public_id, number: parseInt(newNumber) },
      {
        onSuccess: () => {
          toast.success({ message: 'Номер команды успешно изменен!' })
          onClose()
        },
        onError: (error) => {
          toast.error({ message: 'Ошибка при изменении номера команды' })
          console.error('Ошибка при смене номера:', error)
        },
      }
    )
  }

  const handleSwapNumbers = () => {
    if (!swapTeamId) return

    swapTeamNumbers(
      {
        firstTeamPublicId: team.public_id,
        secondTeamPublicId: swapTeamId,
      },
      {
        onSuccess: () => {
          toast.success({ message: 'Номера команд успешно обменены!' })
          onClose()
        },
        onError: (error) => {
          toast.error({ message: 'Ошибка при обмене номеров команд' })
          console.error('Ошибка при смене номеров:', error)
        },
      }
    )
  }

  const handleDeleteTeam = () => {
    deleteTeam(team.public_id, {
      onSuccess: () => {
        toast.success({ message: 'Команда успешно удалена!' })
        onClose()
      },
      onError: (error) => {
        toast.error({ message: 'Ошибка при удалении команды' })
        console.error('Ошибка при удалении команды:', error)
      },
    })
  }

  // Фильтруем команды и удаляем дубликаты по public_id
  const availableTeams = allTeams
    .filter((t) => t.public_id !== team.public_id)
    .filter((team, index, self) => index === self.findIndex((t) => t.public_id === team.public_id))

  return (
    <>
      <UniversalPopup>
        <div className={`${styles.wrapper} ${theme ? styles.wrapperWhite : ''}`}>
          <button className={styles.closeBtn} onClick={onClose} type="button" aria-label="Закрыть" />

          <h2 className={styles.title}>Управление командой #{team.number}</h2>

          <div className={styles.content}>
            {/* Смена номера команды */}
            <div className={styles.section}>
              <h3 className={styles.sectionTitle}>Сменить номер команды</h3>
              <p className={styles.description}>Введите новый номер для команды:</p>
              <div className={styles.inputGroup}>
                <input
                  type="number"
                  value={newNumber}
                  onChange={(e) => setNewNumber(e.target.value)}
                  placeholder="Новый номер"
                  className={styles.input}
                  min="1"
                />
              </div>
              <button
                className={styles.btn}
                onClick={handleChangeNumber}
                disabled={!newNumber.trim() || isUpdating}
                type="button"
              >
                {isUpdating ? 'Сохранение...' : 'Сменить номер'}
              </button>
            </div>

            {/* Обмен номерами команд */}
            <div className={styles.section}>
              <h3 className={styles.sectionTitle}>Поменять номера местами</h3>
              <p className={styles.description}>Выберите команду для обмена номерами:</p>
              <div className={styles.inputGroup}>
                <select value={swapTeamId} onChange={(e) => setSwapTeamId(e.target.value)} className={styles.select}>
                  <option value="">-- Выберите команду --</option>
                  {availableTeams.map((t, index) => (
                    <option key={`${t.public_id}-${index}`} value={t.public_id}>
                      Команда #{t.number}
                    </option>
                  ))}
                </select>
              </div>
              <button
                className={styles.btn}
                onClick={handleSwapNumbers}
                disabled={!swapTeamId || isSwapping}
                type="button"
              >
                {isSwapping ? 'Обмен...' : 'Поменять местами'}
              </button>
            </div>

            {/* Удаление команды */}
            <div className={styles.section}>
              <h3 className={styles.sectionTitle}>Удалить команду</h3>
              {!showDeleteConfirm ? (
                <>
                  <p className={styles.warning}>Это действие нельзя отменить!</p>
                  <button
                    className={`${styles.btn} ${styles.btnDanger}`}
                    onClick={() => setShowDeleteConfirm(true)}
                    type="button"
                  >
                    Удалить команду
                  </button>
                </>
              ) : (
                <>
                  <p className={styles.description}>Подтвердите удаление команды #{team.number}:</p>
                  <div className={styles.buttonGroup}>
                    <button className={styles.btn} onClick={() => setShowDeleteConfirm(false)} type="button">
                      Отмена
                    </button>
                    <button
                      className={`${styles.btn} ${styles.btnDanger}`}
                      onClick={handleDeleteTeam}
                      disabled={isDeleting}
                      type="button"
                    >
                      {isDeleting ? 'Удаление...' : 'Подтвердить удаление'}
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </UniversalPopup>
    </>
  )
}

export default TeamManagementModal
